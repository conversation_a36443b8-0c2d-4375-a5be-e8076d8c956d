import os
import pandas as pd
import numpy as np
import pandas_ta as ta
from sqlalchemy import create_engine
from datetime import datetime, timedelta
from dotenv import load_dotenv
import warnings

warnings.filterwarnings("ignore")
load_dotenv()

# Database setup
current_directory = os.path.dirname(os.path.abspath(__file__))
instance_folder = os.path.join(current_directory, 'instance')
db_file_path = os.path.join(instance_folder, os.getenv('SQLALCHEMY_DATABASE_URI').replace('sqlite:///', ''))
engine = create_engine(f'sqlite:///{db_file_path}', echo=False)

# Ensure predictions folder exists
predictions_folder = os.getenv('PREDICTIONS_FOLDER', 'predictions')
os.makedirs(predictions_folder, exist_ok=True)

def calculate_technical_indicators(df):
    """Calculate comprehensive technical indicators for trend analysis"""
    
    # Moving Averages
    df['sma_20'] = ta.sma(df['close'], length=20)
    df['sma_50'] = ta.sma(df['close'], length=50)
    df['ema_12'] = ta.ema(df['close'], length=12)
    df['ema_26'] = ta.ema(df['close'], length=26)
    
    # MACD
    macd = ta.macd(df['close'])
    df['macd'] = macd['MACD_12_26_9']
    df['macd_signal'] = macd['MACDs_12_26_9']
    df['macd_histogram'] = macd['MACDh_12_26_9']
    
    # RSI
    df['rsi'] = ta.rsi(df['close'], length=14)
    
    # Bollinger Bands
    bb = ta.bbands(df['close'], length=20)
    df['bb_upper'] = bb['BBU_20_2.0']
    df['bb_middle'] = bb['BBM_20_2.0']
    df['bb_lower'] = bb['BBL_20_2.0']
    df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
    
    # Volume indicators
    df['volume_sma'] = ta.sma(df['volume'], length=20)
    df['volume_ratio'] = df['volume'] / df['volume_sma']
    
    # Price position in Bollinger Bands
    df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
    
    # Average True Range for volatility
    df['atr'] = ta.atr(df['high'], df['low'], df['close'], length=14)
    
    # Stochastic
    stoch = ta.stoch(df['high'], df['low'], df['close'])
    df['stoch_k'] = stoch['STOCHk_14_3_3']
    df['stoch_d'] = stoch['STOCHd_14_3_3']
    
    # Williams %R
    df['williams_r'] = ta.willr(df['high'], df['low'], df['close'], length=14)
    
    # Price Rate of Change
    df['roc_10'] = ta.roc(df['close'], length=10)
    df['roc_20'] = ta.roc(df['close'], length=20)
    
    return df

def calculate_trend_score(df):
    """Calculate trend strength score (0-100)"""
    if len(df) < 50:
        return 0
    
    latest = df.iloc[-1]
    score = 0
    
    # 1. Price vs Moving Averages (25 points)
    if latest['close'] > latest['sma_20']:
        score += 8
    if latest['close'] > latest['sma_50']:
        score += 8
    if latest['sma_20'] > latest['sma_50']:
        score += 9
    
    # 2. MACD Analysis (20 points)
    if latest['macd'] > latest['macd_signal']:
        score += 10
    if latest['macd_histogram'] > 0:
        score += 5
    if df['macd_histogram'].iloc[-1] > df['macd_histogram'].iloc[-2]:
        score += 5
    
    # 3. RSI Analysis (15 points)
    rsi = latest['rsi']
    if 45 <= rsi <= 75:  # Strong but not overbought
        score += 15
    elif 35 <= rsi < 45:  # Moderate
        score += 10
    elif rsi > 75:  # Overbought penalty
        score += 5
    
    # 4. Volume Confirmation (15 points)
    if latest['volume_ratio'] > 1.2:  # Above average volume
        score += 10
    if latest['volume_ratio'] > 1.5:  # High volume
        score += 5
    
    # 5. Momentum (15 points)
    if latest['roc_10'] > 2:  # 10-day momentum > 2%
        score += 8
    if latest['roc_20'] > 5:  # 20-day momentum > 5%
        score += 7
    
    # 6. Bollinger Band Position (10 points)
    bb_pos = latest['bb_position']
    if 0.6 <= bb_pos <= 0.9:  # Upper part but not extreme
        score += 10
    elif 0.4 <= bb_pos < 0.6:  # Middle
        score += 5
    
    return min(score, 100)

def calculate_momentum_score(df):
    """Calculate momentum score (0-100)"""
    if len(df) < 30:
        return 0
    
    score = 0
    
    # Recent performance
    returns_5d = (df['close'].iloc[-1] / df['close'].iloc[-6] - 1) * 100
    returns_10d = (df['close'].iloc[-1] / df['close'].iloc[-11] - 1) * 100
    returns_20d = (df['close'].iloc[-1] / df['close'].iloc[-21] - 1) * 100
    
    # Score based on returns
    if returns_5d > 3: score += 20
    elif returns_5d > 1: score += 15
    elif returns_5d > 0: score += 10
    
    if returns_10d > 5: score += 25
    elif returns_10d > 2: score += 20
    elif returns_10d > 0: score += 15
    
    if returns_20d > 8: score += 30
    elif returns_20d > 4: score += 25
    elif returns_20d > 0: score += 20
    
    # Consistency bonus
    if returns_5d > 0 and returns_10d > 0 and returns_20d > 0:
        score += 25
    
    return min(score, 100)

def calculate_volume_score(df):
    """Calculate volume score (0-100)"""
    if len(df) < 20:
        return 0
    
    latest = df.iloc[-1]
    score = 0
    
    # Current volume vs average
    vol_ratio = latest['volume_ratio']
    if vol_ratio > 2.0: score += 40
    elif vol_ratio > 1.5: score += 30
    elif vol_ratio > 1.2: score += 20
    elif vol_ratio > 1.0: score += 10
    
    # Volume trend (last 5 days vs previous 5 days)
    recent_vol = df['volume'].iloc[-5:].mean()
    prev_vol = df['volume'].iloc[-10:-5].mean()
    vol_trend = (recent_vol / prev_vol - 1) * 100
    
    if vol_trend > 20: score += 30
    elif vol_trend > 10: score += 20
    elif vol_trend > 0: score += 10
    
    # Price-Volume relationship
    price_change = (df['close'].iloc[-1] / df['close'].iloc[-6] - 1) * 100
    if price_change > 0 and vol_ratio > 1.2:  # Price up with volume
        score += 30
    
    return min(score, 100)

def calculate_risk_score(df):
    """Calculate risk score (0-100, higher is better/safer)"""
    if len(df) < 30:
        return 50
    
    latest = df.iloc[-1]
    score = 100
    
    # Volatility penalty
    volatility = df['close'].pct_change().std() * np.sqrt(252) * 100
    if volatility > 50: score -= 30
    elif volatility > 35: score -= 20
    elif volatility > 25: score -= 10
    
    # RSI extremes penalty
    rsi = latest['rsi']
    if rsi > 80: score -= 25
    elif rsi < 20: score -= 25
    elif rsi > 75: score -= 15
    elif rsi < 25: score -= 15
    
    # Drawdown analysis
    rolling_max = df['close'].rolling(window=20).max()
    drawdown = (df['close'] / rolling_max - 1) * 100
    current_drawdown = drawdown.iloc[-1]
    
    if current_drawdown < -15: score -= 25
    elif current_drawdown < -10: score -= 15
    elif current_drawdown < -5: score -= 10
    
    return max(score, 0)

def basic_fundamental_filter(symbol):
    """Basic fundamental screening - returns True if stock passes basic filters"""

    # For now, we'll implement basic filters that can be derived from price data
    # In a real system, you'd connect to fundamental data sources

    try:
        # Get longer term data for fundamental analysis
        query = f"""
        SELECT date, close, volume
        FROM finance_data
        WHERE symbol = '{symbol}'
        ORDER BY date DESC
        LIMIT 500
        """
        df = pd.read_sql(query, engine)

        if len(df) < 100:
            return False

        df = df.sort_values('date').reset_index(drop=True)

        # Basic filters using price data as proxy for fundamental health:

        # 1. Avoid penny stocks (< ₹10)
        current_price = df['close'].iloc[-1]
        if current_price < 10:
            return False

        # 2. Avoid stocks with extreme volatility (potential distress)
        volatility = df['close'].pct_change().std() * np.sqrt(252) * 100
        if volatility > 80:  # More than 80% annual volatility
            return False

        # 3. Avoid stocks in severe long-term decline (potential business issues)
        long_term_return = (df['close'].iloc[-1] / df['close'].iloc[0] - 1) * 100
        if long_term_return < -70:  # Down more than 70% over available period
            return False

        # 4. Require minimum liquidity
        avg_volume = df['volume'].tail(30).mean()
        if avg_volume < 10000:  # Minimum average volume
            return False

        # 5. Avoid stocks with recent major crashes (potential news/fundamental issues)
        recent_max = df['close'].tail(60).max()
        current_price = df['close'].iloc[-1]
        recent_drawdown = (current_price / recent_max - 1) * 100
        if recent_drawdown < -50:  # Down more than 50% from recent high
            return False

        return True

    except Exception as e:
        return False

def rank_stocks_technical():
    """Main function to rank all stocks using technical analysis with fundamental filters"""

    print("Starting Technical Analysis Ranking with Fundamental Filters...")

    # Get all symbols from database
    query = "SELECT DISTINCT symbol FROM finance_data"
    symbols_df = pd.read_sql(query, engine)
    symbols = symbols_df['symbol'].tolist()
    
    if not symbols:
        print("No symbols found in database!")
        return
    
    results = []
    processed = 0
    filtered_out = 0

    for symbol in symbols:
        try:
            # First apply fundamental filter
            if not basic_fundamental_filter(symbol):
                filtered_out += 1
                continue

            # Load data for symbol
            query = f"""
            SELECT date, open, high, low, close, volume
            FROM finance_data
            WHERE symbol = '{symbol}'
            ORDER BY date DESC
            LIMIT 200
            """
            df = pd.read_sql(query, engine)

            if len(df) < 50:  # Need minimum data
                continue
            
            # Sort by date ascending for calculations
            df = df.sort_values('date').reset_index(drop=True)
            df['date'] = pd.to_datetime(df['date'])
            
            # Calculate technical indicators
            df = calculate_technical_indicators(df)
            
            # Calculate scores
            trend_score = calculate_trend_score(df)
            momentum_score = calculate_momentum_score(df)
            volume_score = calculate_volume_score(df)
            risk_score = calculate_risk_score(df)
            
            # Calculate composite score with weights
            composite_score = (
                trend_score * 0.35 +      # 35% weight to trend
                momentum_score * 0.30 +   # 30% weight to momentum  
                volume_score * 0.20 +     # 20% weight to volume
                risk_score * 0.15         # 15% weight to risk (safety)
            )
            
            # Get current price and recent performance
            current_price = df['close'].iloc[-1]
            returns_1w = (df['close'].iloc[-1] / df['close'].iloc[-6] - 1) * 100 if len(df) >= 6 else 0
            returns_1m = (df['close'].iloc[-1] / df['close'].iloc[-21] - 1) * 100 if len(df) >= 21 else 0
            
            results.append({
                'Symbol': symbol,
                'Composite_Score': round(composite_score, 2),
                'Trend_Score': round(trend_score, 2),
                'Momentum_Score': round(momentum_score, 2),
                'Volume_Score': round(volume_score, 2),
                'Risk_Score': round(risk_score, 2),
                'Current_Price': round(current_price, 2),
                'Returns_1W': round(returns_1w, 2),
                'Returns_1M': round(returns_1m, 2),
                'RSI': round(df['rsi'].iloc[-1], 2) if not pd.isna(df['rsi'].iloc[-1]) else 0,
                'Volume_Ratio': round(df['volume_ratio'].iloc[-1], 2) if not pd.isna(df['volume_ratio'].iloc[-1]) else 0
            })
            
            processed += 1
            if processed % 50 == 0:
                print(f"Processed {processed}/{len(symbols)} symbols...")
                
        except Exception as e:
            print(f"Error processing {symbol}: {str(e)}")
            continue
    
    if not results:
        print("No results generated!")
        return
    
    # Create DataFrame and sort by composite score
    results_df = pd.DataFrame(results)
    results_df = results_df.sort_values('Composite_Score', ascending=False).reset_index(drop=True)
    results_df['Rank'] = range(1, len(results_df) + 1)
    
    # Save results
    output_path = os.path.join(predictions_folder, 'technical_analysis_ranking.csv')
    results_df.to_csv(output_path, index=False)
    
    print(f"\nTechnical Analysis Ranking Complete!")
    print(f"Results saved to: {output_path}")
    print(f"Total stocks analyzed: {len(results_df)}")
    
    # Display top 20 results
    print("\n" + "="*100)
    print("TOP 20 STOCKS BY TECHNICAL ANALYSIS RANKING")
    print("="*100)
    
    top_20 = results_df.head(20)
    print(top_20[['Rank', 'Symbol', 'Composite_Score', 'Trend_Score', 'Momentum_Score', 
                  'Volume_Score', 'Current_Price', 'Returns_1W', 'Returns_1M']].to_string(index=False))
    
    return results_df

if __name__ == '__main__':
    rank_stocks_technical()
